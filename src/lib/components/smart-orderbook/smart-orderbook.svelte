<script lang="ts">
	/**
	 * Componente Smart Orderbook - Visualização completa com 4 estados
	 *
	 * Este componente gerencia a exibição do orderbook com seus 4 estados:
	 * - default: Botão para iniciar conexão
	 * - loading: Indicador de carregamento
	 * - running: Tabs com dados do orderbook
	 * - error: Mensagem de erro com retry
	 */

	import { createSmartOrderbookState } from './smart-orderbook.state.svelte.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import Icon from '$lib/components/ui/icon.svelte';
	import ActionWrapper from '$lib/components/ui/actionwrapper.svelte';
	import type { Exchange, TradingPair } from './smart-orderbook.types.js';

	// Estado individual para este slot
	const orderbookState = createSmartOrderbookState();

	// Estado do modal de seleção
	let showModal = $state(false);
	let selectedType = $state<'live' | 'historical'>('live');
	let selectedExchange = $state<Exchange>('binance');
	let selectedPair = $state<TradingPair>({
		base: 'BTC',
		quote: 'USDT',
		symbol: 'BTCUSDT'
	});

	/**
	 * Abre o modal de seleção de orderbook
	 */
	function handleAddOrderbook() {
		showModal = true;
	}

	/**
	 * Confirma a seleção e inicia conexão
	 */
	function handleConfirmSelection() {
		showModal = false;

		if (selectedType === 'historical') {
			// TODO: Implementar historical orderbook
			console.log('Historical orderbook will be implemented soon');
			return;
		}

		// Inicia conexão live
		orderbookState.startConnection({
			exchange: selectedExchange,
			pair: selectedPair,
			type: selectedType
		});

		// Simula carregamento por 2 segundos
		setTimeout(() => {
			// 80% chance de sucesso, 20% de erro (para testar)
			if (Math.random() > 0.2) {
				orderbookState.setConnected();
			} else {
				orderbookState.setError('Connection failed. Please try again.');
			}
		}, 2000);
	}

	/**
	 * Reseta o estado para permitir nova tentativa
	 */
	function handleRetry() {
		orderbookState.reset();
	}
</script>

<!-- Container principal seguindo padrão dark -->
<div
	class="flex h-full w-full items-center justify-center rounded-lg border border-gray-950/30 p-6 opacity-90 transition-all duration-200 hover:border-gray-700/10 hover:opacity-100"
>
	<!-- Estado: DEFAULT - Botão para iniciar -->
	{#if orderbookState.currentState === 'default'}
		<ActionWrapper btn onclick={handleAddOrderbook}>
			<Icon icon="material-symbols-light:power-plug" class="text-xl" />
			add orderbook
		</ActionWrapper>

		<!-- Estado: LOADING - Indicadores de carregamento -->
	{:else if orderbookState.currentState === 'loading'}
		<div class="flex flex-col items-center space-y-4 text-center">
			<Icon icon="material-symbols-light:sync" class="animate-spin text-3xl text-blue-400" />
			<div class="space-y-1">
				<p class="text-sm text-gray-300">Connecting...</p>
				<p class="text-xs text-gray-500">
					{orderbookState.currentConfig?.exchange} - {orderbookState.currentConfig?.pair.symbol}
				</p>
			</div>
		</div>

		<!-- Estado: ERROR - Mensagem de erro com retry -->
	{:else if orderbookState.currentState === 'error'}
		<div class="flex flex-col items-center space-y-4 text-center">
			<Icon icon="material-symbols-light:error" class="text-3xl text-red-400" />
			<div class="space-y-2">
				<p class="text-sm text-gray-300">Connection Failed</p>
				<p class="text-xs text-gray-500">
					{orderbookState.activeTabState().errorMessage || 'Unknown error'}
				</p>
			</div>
			<ActionWrapper btn onclick={handleRetry}>
				<Icon icon="material-symbols-light:refresh" class="text-lg" />
				retry
			</ActionWrapper>
		</div>

		<!-- Estado: RUNNING - Placeholder para tabs futuras -->
	{:else if orderbookState.currentState === 'running'}
		<div class="flex flex-col items-center space-y-4 text-center">
			<div class="flex items-center space-x-2">
				<div class="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
				<p class="text-sm text-gray-300">Connected</p>
			</div>
			<div class="space-y-1">
				<p class="text-xs text-gray-500">
					{orderbookState.currentConfig?.exchange} - {orderbookState.currentConfig?.pair.symbol}
				</p>
				<p class="text-xs text-gray-600">Tabs will be implemented next</p>
			</div>
			<ActionWrapper btn onclick={handleRetry}>
				<Icon icon="material-symbols-light:close" class="text-lg" />
				close
			</ActionWrapper>
		</div>
	{/if}
</div>

<!-- Modal de Seleção de Orderbook -->
<Dialog.Root bind:open={showModal}>
	<Dialog.Content class="max-w-md border border-gray-950/30 bg-gray-950/90 text-gray-200">
		<Dialog.Header>
			<Dialog.Title class="text-lg font-medium text-gray-200">Add Orderbook</Dialog.Title>
			<Dialog.Description class="text-sm text-gray-400">
				Choose the type of orderbook and configure the connection
			</Dialog.Description>
		</Dialog.Header>

		<div class="space-y-6 py-4">
			<!-- Tipo de Orderbook -->
			<div class="space-y-3">
				<p class="text-sm font-medium text-gray-300">Orderbook Type</p>
				<div class="grid grid-cols-2 gap-2">
					<Button
						variant={selectedType === 'live' ? 'default' : 'outline'}
						onclick={() => (selectedType = 'live')}
						class="h-auto flex-col space-y-1 p-3"
					>
						<Icon icon="material-symbols-light:radio-button-checked" class="text-lg" />
						<span class="text-xs">Live</span>
					</Button>
					<Button
						variant={selectedType === 'historical' ? 'default' : 'outline'}
						onclick={() => (selectedType = 'historical')}
						class="h-auto flex-col space-y-1 p-3"
						disabled
					>
						<Icon icon="material-symbols-light:history" class="text-lg" />
						<span class="text-xs">Historical</span>
						<span class="text-xs text-gray-500">(Soon)</span>
					</Button>
				</div>
			</div>

			<!-- Exchange & Pair Info -->
			<div class="space-y-3">
				<p class="text-sm font-medium text-gray-300">Connection Details</p>
				<div class="rounded-lg border border-gray-950/30 bg-gray-950/20 p-3">
					<div class="space-y-2 text-sm">
						<div class="flex justify-between">
							<span class="text-gray-400">Exchange:</span>
							<span class="text-gray-200">{selectedExchange}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-400">Pair:</span>
							<span class="text-gray-200">{selectedPair.symbol}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-400">Type:</span>
							<span class="text-gray-200 capitalize">{selectedType}</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<Dialog.Footer class="flex justify-end space-x-2">
			<Button variant="outline" onclick={() => (showModal = false)}>Cancel</Button>
			<Button onclick={handleConfirmSelection} disabled={selectedType === 'historical'}>
				Connect
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
