<script lang="ts">
	/**
	 * Componente Smart Orderbook - Visualização completa com 4 estados
	 *
	 * Este componente gerencia a exibição do orderbook com seus 4 estados:
	 * - default: Botão para iniciar conexão
	 * - loading: Indicador de carregamento
	 * - running: Tabs com dados do orderbook
	 * - error: Mensagem de erro com retry
	 */

	import { smartOrderbookState } from './smart-orderbook.state.svelte.js';
	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import * as Card from '$lib/components/ui/card/index.js';
	import * as Alert from '$lib/components/ui/alert/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import Icon from '$lib/components/ui/icon.svelte';

	/**
	 * Simula o início de uma conexão de orderbook
	 *
	 * Por enquanto usa dados mock para demonstrar o fluxo de estados.
	 * Futuramente será conectado com websockets reais.
	 */
	function handleStartConnection() {
		// Inicia com configuração mock
		smartOrderbookState.startConnection({
			exchange: 'binance',
			pair: {
				base: 'BTC',
				quote: 'USDT',
				symbol: 'BTCUSDT'
			},
			type: 'live'
		});

		// Simula carregamento por 2 segundos
		setTimeout(() => {
			// 80% chance de sucesso, 20% de erro (para testar)
			if (Math.random() > 0.2) {
				smartOrderbookState.setConnected();
			} else {
				smartOrderbookState.setError('Falha na conexão com Binance. Tente novamente.');
			}
		}, 2000);
	}

	/**
	 * Reseta o estado para permitir nova tentativa
	 */
	function handleRetry() {
		smartOrderbookState.reset();
	}

	/**
	 * Muda a tab ativa
	 */
	function handleTabChange(tabIndex: number) {
		smartOrderbookState.setActiveTab(tabIndex);
	}
</script>

<!-- Container principal do Smart Orderbook -->
<div
	class="flex h-full w-full flex-col rounded-lg border border-gray-950/50 bg-gradient-to-b from-gray-900/10 to-gray-950/10 p-4"
>
	<!-- Estado: DEFAULT - Botão para iniciar -->
	{#if smartOrderbookState.currentState === 'default'}
		<div class="flex h-full items-center justify-center">
			<div class="space-y-4 text-center">
				<Icon icon="material-symbols-light:power-plug" class="mx-auto text-4xl text-gray-400" />
				<div class="space-y-2">
					<h3 class="text-lg font-medium text-gray-300">Smart Orderbook</h3>
					<p class="text-sm text-gray-500">
						Conecte-se a uma exchange para visualizar dados em tempo real
					</p>
				</div>
				<Button onclick={handleStartConnection} class="mt-4">
					<Icon icon="material-symbols-light:play-arrow" class="mr-2 text-lg" />
					Iniciar Conexão
				</Button>
			</div>
		</div>

		<!-- Estado: LOADING - Indicadores de carregamento -->
	{:else if smartOrderbookState.currentState === 'loading'}
		<div class="flex h-full flex-col">
			<!-- Header com info da conexão -->
			<div class="mb-4 flex items-center justify-between">
				<div class="flex items-center space-x-2">
					<Icon icon="material-symbols-light:sync" class="animate-spin text-lg text-blue-400" />
					<span class="text-sm text-gray-300">
						Conectando em {smartOrderbookState.currentConfig?.exchange} - {smartOrderbookState
							.currentConfig?.pair.symbol}
					</span>
				</div>
			</div>

			<!-- Skeleton das tabs -->
			<div class="space-y-4">
				<div class="flex space-x-1">
					<Skeleton class="h-10 w-24" />
					<Skeleton class="h-10 w-24" />
					<Skeleton class="h-10 w-24" />
					<Skeleton class="h-10 w-24" />
				</div>
				<div class="space-y-3">
					<Skeleton class="h-6 w-full" />
					<Skeleton class="h-6 w-3/4" />
					<Skeleton class="h-6 w-1/2" />
					<Skeleton class="h-32 w-full" />
				</div>
			</div>
		</div>

		<!-- Estado: ERROR - Mensagem de erro com retry -->
	{:else if smartOrderbookState.currentState === 'error'}
		<div class="flex h-full items-center justify-center">
			<div class="w-full max-w-md">
				<Alert.Root variant="destructive">
					<Icon icon="material-symbols-light:error" class="h-4 w-4" />
					<Alert.Title>Erro na Conexão</Alert.Title>
					<Alert.Description>
						{smartOrderbookState.activeTabState().errorMessage || 'Erro desconhecido'}
					</Alert.Description>
				</Alert.Root>

				<div class="mt-4 flex justify-center space-x-2">
					<Button variant="outline" onclick={handleRetry}>
						<Icon icon="material-symbols-light:refresh" class="mr-2 text-lg" />
						Tentar Novamente
					</Button>
				</div>
			</div>
		</div>

		<!-- Estado: RUNNING - Tabs com dados -->
	{:else if smartOrderbookState.currentState === 'running'}
		<div class="flex h-full flex-col">
			<!-- Header com info da conexão -->
			<div class="mb-4 flex items-center justify-between">
				<div class="flex items-center space-x-2">
					<div class="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
					<span class="text-sm text-gray-300">
						{smartOrderbookState.currentConfig?.exchange} - {smartOrderbookState.currentConfig?.pair
							.symbol}
					</span>
				</div>
				<Button variant="ghost" size="sm" onclick={handleRetry}>
					<Icon icon="material-symbols-light:close" class="text-lg" />
				</Button>
			</div>

			<!-- Tabs do Orderbook -->
			<Tabs.Root value={smartOrderbookState.currentTab.toString()} class="flex-1">
				<Tabs.List class="grid w-full grid-cols-4">
					<Tabs.Trigger value="0" onclick={() => handleTabChange(0)}>Orderbook</Tabs.Trigger>
					<Tabs.Trigger value="1" onclick={() => handleTabChange(1)}>Spread</Tabs.Trigger>
					<Tabs.Trigger value="2" onclick={() => handleTabChange(2)}>Imbalance</Tabs.Trigger>
					<Tabs.Trigger value="3" onclick={() => handleTabChange(3)}>Volume</Tabs.Trigger>
				</Tabs.List>

				<!-- Tab 1: Orderbook View -->
				<Tabs.Content value="0" class="mt-4 flex-1">
					<Card.Root class="h-full">
						<Card.Header>
							<Card.Title class="flex items-center space-x-2">
								<Icon icon="material-symbols-light:list" class="text-lg" />
								<span>Orderbook Live</span>
							</Card.Title>
							<Card.Description>Visualização em tempo real dos bids e asks</Card.Description>
						</Card.Header>
						<Card.Content>
							<div class="py-8 text-center text-gray-500">
								📊 Dados do orderbook aparecerão aqui
							</div>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				<!-- Tab 2: Spread View -->
				<Tabs.Content value="1" class="mt-4 flex-1">
					<Card.Root class="h-full">
						<Card.Header>
							<Card.Title class="flex items-center space-x-2">
								<Icon icon="material-symbols-light:trending-up" class="text-lg" />
								<span>Spread Evolution</span>
							</Card.Title>
							<Card.Description>Evolução do spread nos últimos 5 minutos</Card.Description>
						</Card.Header>
						<Card.Content>
							<div class="py-8 text-center text-gray-500">📈 Gráfico do spread aparecerá aqui</div>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				<!-- Tab 3: Imbalance View -->
				<Tabs.Content value="2" class="mt-4 flex-1">
					<Card.Root class="h-full">
						<Card.Header>
							<Card.Title class="flex items-center space-x-2">
								<Icon icon="material-symbols-light:balance" class="text-lg" />
								<span>Imbalance Analysis</span>
							</Card.Title>
							<Card.Description>Análise do desequilíbrio do orderbook</Card.Description>
						</Card.Header>
						<Card.Content>
							<div class="py-8 text-center text-gray-500">
								⚖️ Análise de imbalance aparecerá aqui
							</div>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				<!-- Tab 4: Volume Evolution -->
				<Tabs.Content value="3" class="mt-4 flex-1">
					<Card.Root class="h-full">
						<Card.Header>
							<Card.Title class="flex items-center space-x-2">
								<Icon icon="material-symbols-light:analytics" class="text-lg" />
								<span>Volume Heatmap</span>
							</Card.Title>
							<Card.Description>Heatmap do volume dos últimos 30 dias</Card.Description>
						</Card.Header>
						<Card.Content>
							<div class="py-8 text-center text-gray-500">🔥 Heatmap de volume aparecerá aqui</div>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>
			</Tabs.Root>
		</div>
	{/if}
</div>
