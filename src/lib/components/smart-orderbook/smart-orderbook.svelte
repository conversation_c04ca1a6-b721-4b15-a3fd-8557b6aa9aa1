<script lang="ts">
	/**
	 * Componente Smart Orderbook - Visualização completa com 4 estados
	 *
	 * Este componente gerencia a exibição do orderbook com seus 4 estados:
	 * - default: Botão para iniciar conexão
	 * - loading: Indicador de carregamento
	 * - running: Tabs com dados do orderbook
	 * - error: Mensagem de erro com retry
	 */

	import { createSmartOrderbookState } from './smart-orderbook.state.svelte.js';
	import Icon from '$lib/components/ui/icon.svelte';
	import ActionWrapper from '$lib/components/ui/actionwrapper.svelte';

	// Estado individual para este slot
	const orderbookState = createSmartOrderbookState();

	/**
	 * Simula o início de uma conexão de orderbook
	 *
	 * Por enquanto usa dados mock para demonstrar o fluxo de estados.
	 * Futuramente será conectado com websockets reais.
	 */
	function handleStartConnection() {
		// Inicia com configuração mock
		orderbookState.startConnection({
			exchange: 'binance',
			pair: {
				base: 'BTC',
				quote: 'USDT',
				symbol: 'BTCUSDT'
			},
			type: 'live'
		});

		// Simula carregamento por 2 segundos
		setTimeout(() => {
			// 80% chance de sucesso, 20% de erro (para testar)
			if (Math.random() > 0.2) {
				orderbookState.setConnected();
			} else {
				orderbookState.setError('Connection failed. Please try again.');
			}
		}, 2000);
	}

	/**
	 * Reseta o estado para permitir nova tentativa
	 */
	function handleRetry() {
		orderbookState.reset();
	}
</script>

<!-- Container principal seguindo padrão dark -->
<div
	class="flex h-full w-full items-center justify-center rounded-lg border border-gray-950/30 p-6 opacity-90 transition-all duration-200 hover:border-gray-700/10 hover:opacity-100"
>
	<!-- Estado: DEFAULT - Botão para iniciar -->
	{#if orderbookState.currentState === 'default'}
		<ActionWrapper btn onclick={handleStartConnection}>
			<Icon icon="material-symbols-light:power-plug" class="text-xl" />
			add orderbook
		</ActionWrapper>

		<!-- Estado: LOADING - Indicadores de carregamento -->
	{:else if orderbookState.currentState === 'loading'}
		<div class="flex flex-col items-center space-y-4 text-center">
			<Icon icon="material-symbols-light:sync" class="animate-spin text-3xl text-blue-400" />
			<div class="space-y-1">
				<p class="text-sm text-gray-300">Connecting...</p>
				<p class="text-xs text-gray-500">
					{orderbookState.currentConfig?.exchange} - {orderbookState.currentConfig?.pair.symbol}
				</p>
			</div>
		</div>

		<!-- Estado: ERROR - Mensagem de erro com retry -->
	{:else if orderbookState.currentState === 'error'}
		<div class="flex flex-col items-center space-y-4 text-center">
			<Icon icon="material-symbols-light:error" class="text-3xl text-red-400" />
			<div class="space-y-2">
				<p class="text-sm text-gray-300">Connection Failed</p>
				<p class="text-xs text-gray-500">
					{orderbookState.activeTabState().errorMessage || 'Unknown error'}
				</p>
			</div>
			<ActionWrapper btn onclick={handleRetry}>
				<Icon icon="material-symbols-light:refresh" class="text-lg" />
				retry
			</ActionWrapper>
		</div>

		<!-- Estado: RUNNING - Placeholder para tabs futuras -->
	{:else if orderbookState.currentState === 'running'}
		<div class="flex flex-col items-center space-y-4 text-center">
			<div class="flex items-center space-x-2">
				<div class="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
				<p class="text-sm text-gray-300">Connected</p>
			</div>
			<div class="space-y-1">
				<p class="text-xs text-gray-500">
					{orderbookState.currentConfig?.exchange} - {orderbookState.currentConfig?.pair.symbol}
				</p>
				<p class="text-xs text-gray-600">Tabs will be implemented next</p>
			</div>
			<ActionWrapper btn onclick={handleRetry}>
				<Icon icon="material-symbols-light:close" class="text-lg" />
				close
			</ActionWrapper>
		</div>
	{/if}
</div>
