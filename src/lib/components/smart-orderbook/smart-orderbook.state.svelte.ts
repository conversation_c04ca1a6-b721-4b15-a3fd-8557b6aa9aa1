/**
 * Estado reativo do Smart Orderbook usando Svelte 5
 *
 * Gerencia todo o estado do componente de forma centralizada e reativa.
 * Cada slot do orderbook tem seu próprio estado independente.
 */

import type { SmartOrderbookState, OrderbookConfig } from './smart-orderbook.types.js';

/**
 * Cria um estado inicial limpo para o orderbook
 *
 * @returns Estado inicial com todas as tabs em estado padrão
 *
 * @example
 * ```ts
 * const estado = createInitialState();
 * console.log(estado.state); // 'default'
 * ```
 */
function createInitialState(): SmartOrderbookState {
    return {
        state: 'default',
        config: undefined,
        activeTab: 0,
        tabs: {
            orderbook: {
                isActive: true,
                isLoading: false,
                hasError: false
            },
            spread: {
                isActive: false,
                isLoading: false,
                hasError: false
            },
            imbalance: {
                isActive: false,
                isLoading: false,
                hasError: false
            },
            volume: {
                isActive: false,
                isLoading: false,
                hasError: false
            }
        }
    };
}

/**
 * Cria e gerencia o estado de um slot do Smart Orderbook
 *
 * @returns Objeto com estado reativo e métodos para controlá-lo
 *
 * @example
 * ```ts
 * const { state, startConnection, setError } = createSmartOrderbookState();
 *
 * // Iniciar conexão
 * startConnection({ exchange: 'binance', pair: { symbol: 'BTCUSDT' } });
 *
 * // Verificar estado
 * console.log(state.state); // 'loading'
 * ```
 */
export function createSmartOrderbookState() {
    let state = $state<SmartOrderbookState>(createInitialState());

    return {
        // Estado reativo (readonly para componentes)
        get state() {
            return state;
        },

        /**
         * Inicia uma nova conexão de orderbook
         *
         * Muda o estado para 'loading' e configura os parâmetros da conexão.
         *
         * @param config - Configuração da exchange e par
         *
         * @example
         * ```ts
         * startConnection({
         *   exchange: 'binance',
         *   pair: { base: 'BTC', quote: 'USDT', symbol: 'BTCUSDT' },
         *   type: 'live'
         * });
         * ```
         */
        startConnection(config: OrderbookConfig) {
            state.state = 'loading';
            state.config = config;

            // Reset todas as tabs para estado inicial
            Object.values(state.tabs).forEach(tab => {
                tab.hasError = false;
                tab.errorMessage = undefined;
                tab.isLoading = true;
            });
        },

        /**
         * Marca a conexão como estabelecida e funcionando
         *
         * Muda o estado para 'running' indicando que os dados estão fluindo.
         *
         * @example
         * ```ts
         * setConnected(); // Estado muda para 'running'
         * ```
         */
        setConnected() {
            state.state = 'running';

            // Marca todas as tabs como carregadas
            Object.values(state.tabs).forEach(tab => {
                tab.isLoading = false;
                tab.lastUpdate = new Date();
            });
        },

        /**
         * Define um erro e permite retry
         *
         * Muda o estado para 'error' e armazena a mensagem de erro.
         *
         * @param message - Mensagem de erro para exibir ao usuário
         *
         * @example
         * ```ts
         * setError('Falha na conexão com Binance');
         * ```
         */
        setError(message: string) {
            state.state = 'error';

            // Marca todas as tabs com erro
            Object.values(state.tabs).forEach(tab => {
                tab.hasError = true;
                tab.errorMessage = message;
                tab.isLoading = false;
            });
        },

        /**
         * Reseta o estado para inicial (botão de retry)
         *
         * Volta ao estado 'default' limpando configurações e erros.
         *
         * @example
         * ```ts
         * reset(); // Volta ao estado inicial
         * ```
         */
        reset() {
            state = createInitialState();
        },

        /**
         * Muda a tab ativa
         *
         * @param tabIndex - Índice da tab (0-3)
         *
         * @example
         * ```ts
         * setActiveTab(1); // Muda para tab de spread
         * ```
         */
        setActiveTab(tabIndex: number) {
            if (tabIndex >= 0 && tabIndex <= 3) {
                state.activeTab = tabIndex;

                // Atualiza estado das tabs
                const tabNames = ['orderbook', 'spread', 'imbalance', 'volume'] as const;
                tabNames.forEach((name, index) => {
                    state.tabs[name].isActive = index === tabIndex;
                });
            }
        }
    };
}