/**
 * Estado reativo do Smart Orderbook usando Svelte 5 com Classes
 *
 * Gerencia todo o estado do componente de forma centralizada e reativa.
 * Cada slot do orderbook tem seu próprio estado independente.
 */

import type { OrderbookConfig, OrderbookSlotState } from './smart-orderbook.types.js';

/**
 * Classe que gerencia o estado completo de um slot do Smart Orderbook
 *
 * Usa runes do Svelte 5 ($state) para reatividade automática.
 * C<PERSON> instância representa um slot independente na grid 4x1.
 *
 * @example
 * ```ts
 * const orderbook = new SmartOrderbookState();
 *
 * // Iniciar conexão
 * orderbook.startConnection({
 *   exchange: 'binance',
 *   pair: { base: 'BTC', quote: 'USDT', symbol: 'BTCUSDT' },
 *   type: 'live'
 * });
 *
 * // Verificar estado reativo
 * console.log(orderbook.currentState); // 'loading'
 * ```
 */
class SmartOrderbookState {
    // Estados reativos usando runes do Svelte 5
    state = $state<OrderbookSlotState>('default');
    config = $state<OrderbookConfig | undefined>(undefined);
    activeTab = $state<number>(0);

    // Estados das 4 tabs
    tabs = $state({
        orderbook: {
            isActive: true,
            isLoading: false,
            hasError: false,
            errorMessage: undefined as string | undefined,
            lastUpdate: undefined as Date | undefined
        },
        spread: {
            isActive: false,
            isLoading: false,
            hasError: false,
            errorMessage: undefined as string | undefined,
            lastUpdate: undefined as Date | undefined
        },
        imbalance: {
            isActive: false,
            isLoading: false,
            hasError: false,
            errorMessage: undefined as string | undefined,
            lastUpdate: undefined as Date | undefined
        },
        volume: {
            isActive: false,
            isLoading: false,
            hasError: false,
            errorMessage: undefined as string | undefined,
            lastUpdate: undefined as Date | undefined
        }
    });

    /**
     * Getter para acessar o estado atual de forma reativa
     *
     * @returns Estado atual do slot
     *
     * @example
     * ```ts
     * if (orderbook.currentState === 'running') {
     *   console.log('Orderbook está funcionando!');
     * }
     * ```
     */
    get currentState(): OrderbookSlotState {
        return this.state;
    }

    /**
     * Getter para acessar a configuração atual
     *
     * @returns Configuração ativa ou undefined
     */
    get currentConfig(): OrderbookConfig | undefined {
        return this.config;
    }

    /**
     * Inicia uma nova conexão de orderbook
     *
     * Muda o estado para 'loading' e configura os parâmetros da conexão.
     * Reseta todas as tabs para estado de carregamento.
     *
     * @param config - Configuração da exchange e par
     *
     * @example
     * ```ts
     * orderbook.startConnection({
     *   exchange: 'binance',
     *   pair: { base: 'BTC', quote: 'USDT', symbol: 'BTCUSDT' },
     *   type: 'live'
     * });
     * ```
     */
    startConnection = (config: OrderbookConfig) => {
        this.state = 'loading';
        this.config = config;

        // Reset todas as tabs para estado inicial
        Object.values(this.tabs).forEach(tab => {
            tab.hasError = false;
            tab.errorMessage = undefined;
            tab.isLoading = true;
        });
    };

    /**
     * Marca a conexão como estabelecida e funcionando
     *
     * Muda o estado para 'running' indicando que os dados estão fluindo.
     * Atualiza timestamp de todas as tabs.
     *
     * @example
     * ```ts
     * orderbook.setConnected(); // Estado muda para 'running'
     * ```
     */
    setConnected = () => {
        this.state = 'running';

        // Marca todas as tabs como carregadas
        Object.values(this.tabs).forEach(tab => {
            tab.isLoading = false;
            tab.lastUpdate = new Date();
        });
    };

    /**
     * Define um erro e permite retry
     *
     * Muda o estado para 'error' e armazena a mensagem de erro.
     * Para todas as tabs e marca com erro.
     *
     * @param message - Mensagem de erro para exibir ao usuário
     *
     * @example
     * ```ts
     * orderbook.setError('Falha na conexão com Binance');
     * ```
     */
    setError = (message: string) => {
        this.state = 'error';

        // Marca todas as tabs com erro
        Object.values(this.tabs).forEach(tab => {
            tab.hasError = true;
            tab.errorMessage = message;
            tab.isLoading = false;
        });
    };

    /**
     * Reseta o estado para inicial (botão de retry)
     *
     * Volta ao estado 'default' limpando configurações e erros.
     * Reseta todas as tabs para estado inicial.
     *
     * @example
     * ```ts
     * orderbook.reset(); // Volta ao estado inicial
     * ```
     */
    reset = () => {
        this.state = 'default';
        this.config = undefined;
        this.activeTab = 0;

        // Reset todas as tabs
        this.tabs.orderbook = { isActive: true, isLoading: false, hasError: false, errorMessage: undefined, lastUpdate: undefined };
        this.tabs.spread = { isActive: false, isLoading: false, hasError: false, errorMessage: undefined, lastUpdate: undefined };
        this.tabs.imbalance = { isActive: false, isLoading: false, hasError: false, errorMessage: undefined, lastUpdate: undefined };
        this.tabs.volume = { isActive: false, isLoading: false, hasError: false, errorMessage: undefined, lastUpdate: undefined };
    };

    /**
     * Muda a tab ativa
     *
     * Atualiza qual tab está sendo exibida e marca as outras como inativas.
     *
     * @param tabIndex - Índice da tab (0-3)
     *
     * @example
     * ```ts
     * orderbook.setActiveTab(1); // Muda para tab de spread
     * orderbook.setActiveTab(2); // Muda para tab de imbalance
     * ```
     */
    setActiveTab = (tabIndex: number) => {
        if (tabIndex >= 0 && tabIndex <= 3) {
            this.activeTab = tabIndex;

            // Atualiza estado das tabs
            const tabNames = ['orderbook', 'spread', 'imbalance', 'volume'] as const;
            tabNames.forEach((name, index) => {
                this.tabs[name].isActive = index === tabIndex;
            });
        }
    };
}

/**
 * Instância global exportada do Smart Orderbook State
 *
 * Esta const pode ser importada em qualquer componente para acessar
 * e controlar o estado do orderbook de forma reativa.
 *
 * @example
 * ```ts
 * import { smartOrderbookState } from './smart-orderbook.state.svelte.js';
 *
 * // Usar nos componentes
 * smartOrderbookState.startConnection({ exchange: 'binance', ... });
 *
 * // Estado reativo
 * $: if (smartOrderbookState.currentState === 'running') {
 *   console.log('Conectado!');
 * }
 * ```
 */
export const smartOrderbookState = new SmartOrderbookState();