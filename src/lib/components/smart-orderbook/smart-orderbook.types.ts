/**
 * Tipos base para o sistema de Smart Orderbook
 *
 * Este arquivo define todos os tipos TypeScript necessários para o funcionamento
 * do componente de orderbook inteligente com suas 4 tabs especializadas.
 */

/** Estados possíveis de cada slot do orderbook */
export type OrderbookSlotState = 'default' | 'loading' | 'running' | 'error';

/** Tipos de orderbook disponíveis */
export type OrderbookType = 'live' | 'historical';

/** Exchanges suportadas */
export type Exchange = 'binance' | 'coinbase' | 'kraken' | 'bybit';

/** Configuração básica de um par de trading */
export interface TradingPair {
    /** Moeda base (ex: BTC) */
    base: string;
    /** Moeda de cotação (ex: USDT) */
    quote: string;
    /** Nome completo do par (ex: BTCUSDT) */
    symbol: string;
}

/** Configuração de conexão do orderbook */
export interface OrderbookConfig {
    /** Exchange selecionada */
    exchange: Exchange;
    /** Par de trading */
    pair: TradingPair;
    /** Tipo de orderbook */
    type: OrderbookType;
}

/** Estado de uma tab individual */
export interface TabState {
    /** Se a tab está ativa */
    isActive: boolean;
    /** Se está carregando dados */
    isLoading: boolean;
    /** Se tem erro */
    hasError: boolean;
    /** Mensagem de erro (se houver) */
    errorMessage?: string;
    /** Última atualização */
    lastUpdate?: Date;
}

/** Estado completo do smart orderbook */
export interface SmartOrderbookState {
    /** Estado atual do slot */
    state: OrderbookSlotState;
    /** Configuração ativa */
    config?: OrderbookConfig;
    /** Tab atualmente selecionada (0-3) */
    activeTab: number;
    /** Estados das 4 tabs */
    tabs: {
        /** Tab 1: Visualização do orderbook */
        orderbook: TabState;
        /** Tab 2: Visualização do spread */
        spread: TabState;
        /** Tab 3: Visualização do imbalance */
        imbalance: TabState;
        /** Tab 4: Evolução do volume */
        volume: TabState;
    };
}

/** Dados de uma entrada do orderbook (bid ou ask) */
export interface OrderbookEntry {
    /** Preço */
    price: number;
    /** Quantidade */
    quantity: number;
    /** Total acumulado */
    total: number;
}

/** Dados completos do orderbook */
export interface OrderbookData {
    /** Lista de bids (compras) ordenada por preço decrescente */
    bids: OrderbookEntry[];
    /** Lista de asks (vendas) ordenada por preço crescente */
    asks: OrderbookEntry[];
    /** Timestamp da última atualização */
    timestamp: number;
}