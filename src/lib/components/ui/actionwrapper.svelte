<script lang="ts">
	import { cn } from '$lib/utils';
	import type { Snippet } from 'svelte';

	interface ActionWrapperProps {
		class?: string;
		children: Snippet;
		btn?: boolean;
		onclick?: () => void;
		href?: string;
	}

	const { class: className, children, btn, onclick, href }: ActionWrapperProps = $props();
</script>

{#if btn}
	<button
		class={cn(
			'flex size-fit cursor-pointer items-center gap-2 rounded-lg border border-gray-950/50 bg-gradient-to-b from-gray-900/30 to-gray-950/20 p-4 text-sm opacity-90 hover:border-gray-700/20',
			className
		)}
		{onclick}
	>
		{@render children()}
	</button>
{:else if href}
	<a
		class={cn(
			'size-[320px] rounded-lg border border-gray-950/50 bg-gradient-to-b from-gray-900/30 to-gray-950/20 p-4 opacity-90',
			className
		)}
		{href}
	>
		{@render children()}
	</a>
{:else}
	<div
		class={cn(
			'size-[320px] rounded-lg border border-gray-950/50 bg-gradient-to-b from-gray-900/30 to-gray-950/20 p-4 opacity-90',
			className
		)}
	>
		{@render children()}
	</div>
{/if}
