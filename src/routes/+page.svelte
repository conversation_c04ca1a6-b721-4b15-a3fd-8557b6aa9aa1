<script>
	import ActionWrapper from '$lib/components/ui/actionwrapper.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
</script>

<section class="h-full p-3">
	<!-- 4-Part Orderbook Grid -->
	<div class="grid h-full grid-cols-1 gap-4 md:grid-cols-2">
		<!-- Slot 1 -->
		<div
			class="flex min-h-[300px] items-center justify-center rounded-lg border border-gray-950/50 p-6 opacity-90 transition-all duration-200 hover:border-gray-700/10 hover:opacity-100"
		>
			<ActionWrapper btn>
				<Icon icon="material-symbols-light:power-plug" class="text-xl" />
				init orderbook
			</ActionWrapper>
		</div>

		<!-- Slot 2 -->
		<div
			class="flex min-h-[300px] items-center justify-center rounded-lg border border-gray-950/50 p-6 opacity-90 transition-all duration-200 hover:border-gray-700/10 hover:opacity-100"
		>
			<ActionWrapper btn>
				<Icon icon="material-symbols-light:power-plug" class="text-xl" />
				init orderbook
			</ActionWrapper>
		</div>

		<!-- Slot 3 -->
		<div
			class="flex min-h-[300px] items-center justify-center rounded-lg border border-gray-950/50 p-6 opacity-90 transition-all duration-200 hover:border-gray-700/10 hover:opacity-100"
		>
			<ActionWrapper btn>
				<Icon icon="material-symbols-light:power-plug" class="text-xl" />
				init orderbook
			</ActionWrapper>
		</div>

		<!-- Slot 4 -->
		<div
			class="flex min-h-[300px] items-center justify-center rounded-lg border border-gray-950/50 p-6 opacity-90 transition-all duration-200 hover:border-gray-700/10 hover:opacity-100"
		>
			<ActionWrapper btn>
				<Icon icon="material-symbols-light:power-plug" class="text-xl" />
				init orderbook
			</ActionWrapper>
		</div>
	</div>
</section>
