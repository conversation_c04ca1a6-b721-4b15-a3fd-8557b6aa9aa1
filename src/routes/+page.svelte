<script>
    import ActionWrapper from '$lib/components/ui/actionwrapper.svelte';
    import Icon from '$lib/components/ui/icon.svelte';
</script>

<section class="grid grid-cols-1 md:grid-cols-2 h-full gap-px bg-gray-200">
    <div class="bg-white flex items-center justify-center border border-gray-100">
        <ActionWrapper btn>
            <Icon icon="material-symbols-light:order-play-outline-sharp" class="text-2xl" />
            init orderbook
        </ActionWrapper>
    </div>
    <div class="bg-white flex items-center justify-center border border-gray-100">
        <ActionWrapper btn>
            <Icon icon="material-symbols-light:order-play-outline-sharp" class="text-2xl" />
            init orderbook
        </ActionWrapper>
    </div>
    <div class="bg-white flex items-center justify-center border border-gray-100">
        <ActionWrapper btn>
            <Icon icon="material-symbols-light:order-play-outline-sharp" class="text-2xl" />
            init orderbook
        </ActionWrapper>
    </div>
    <div class="bg-white flex items-center justify-center border border-gray-100">
        <ActionWrapper btn>
            <Icon icon="material-symbols-light:order-play-outline-sharp" class="text-2xl" />
            init orderbook
        </ActionWrapper>
    </div>
</section>
